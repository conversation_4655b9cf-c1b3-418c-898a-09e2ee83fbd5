//
//  StatsAnalyzer.swift
//  AttemptLog
//
//  Created by tyu<PERSON> on 2025/5/24.
//

import Foundation

// MARK: - Statistics Data Models

struct SuccessRateData {
    let date: Date
    let successRate: Double
    let totalAttempts: Int
    let successfulAttempts: Int
}

struct LandingDistribution {
    let centerCount: Int
    let leftCount: Int
    let rightCount: Int
    let outOfBoundsCount: Int
    
    var total: Int {
        return centerCount + leftCount + rightCount + outOfBoundsCount
    }
    
    var centerPercentage: Double {
        guard total > 0 else { return 0 }
        return Double(centerCount) / Double(total) * 100
    }
    
    var leftPercentage: Double {
        guard total > 0 else { return 0 }
        return Double(leftCount) / Double(total) * 100
    }
    
    var rightPercentage: Double {
        guard total > 0 else { return 0 }
        return Double(rightCount) / Double(total) * 100
    }
    
    var outOfBoundsPercentage: Double {
        guard total > 0 else { return 0 }
        return Double(outOfBoundsCount) / Double(total) * 100
    }
}

struct TopAttempt {
    let attempt: AttemptLog
    let rank: Int
}

struct ErrorTypeStats {
    let errorType: String
    let count: Int
    let percentage: Double
}

struct TrendData {
    let period: String
    let value: Double
    let date: Date
}

// MARK: - Statistics Analyzer

class StatsAnalyzer {
    static let shared = StatsAnalyzer()
    
    private init() {}
    
    // MARK: - Success Rate Analysis
    
    func getSuccessRateTrend(period: StatsPeriod = .last30Days) -> [SuccessRateData] {
        let attempts = AttemptLogManager.shared.attempts
        let filteredAttempts = filterAttemptsByPeriod(attempts, period: period)
        
        let groupedByDate = Dictionary(grouping: filteredAttempts) { attempt in
            Calendar.current.startOfDay(for: attempt.date)
        }
        
        return groupedByDate.map { date, dayAttempts in
            let successfulCount = dayAttempts.filter { $0.isSuccessful }.count
            let successRate = Double(successfulCount) / Double(dayAttempts.count)
            
            return SuccessRateData(
                date: date,
                successRate: successRate,
                totalAttempts: dayAttempts.count,
                successfulAttempts: successfulCount
            )
        }.sorted { $0.date < $1.date }
    }
    
    // MARK: - Landing Distribution Analysis
    
    func getLandingDistribution(period: StatsPeriod = .allTime) -> LandingDistribution {
        let attempts = AttemptLogManager.shared.attempts
        let filteredAttempts = filterAttemptsByPeriod(attempts, period: period)
        
        let centerCount = filteredAttempts.filter { $0.landingArea == .center }.count
        let leftCount = filteredAttempts.filter { $0.landingArea == .leftSide }.count
        let rightCount = filteredAttempts.filter { $0.landingArea == .rightSide }.count
        let outOfBoundsCount = filteredAttempts.filter { $0.landingArea == .outOfBounds }.count
        
        return LandingDistribution(
            centerCount: centerCount,
            leftCount: leftCount,
            rightCount: rightCount,
            outOfBoundsCount: outOfBoundsCount
        )
    }
    
    // MARK: - Top Attempts Analysis
    
    func getTopAttempts(limit: Int = 5) -> [TopAttempt] {
        let attempts = AttemptLogManager.shared.attempts
        let sortedAttempts = attempts.sorted { $0.technicalRating > $1.technicalRating }
        
        return Array(sortedAttempts.prefix(limit)).enumerated().map { index, attempt in
            TopAttempt(attempt: attempt, rank: index + 1)
        }
    }
    
    // MARK: - Error Type Analysis
    
    func getErrorTypeStats(period: StatsPeriod = .allTime) -> [ErrorTypeStats] {
        let attempts = AttemptLogManager.shared.attempts
        let filteredAttempts = filterAttemptsByPeriod(attempts, period: period)
        
        // Analyze common error patterns from notes and technical parameters
        var errorCounts: [String: Int] = [:]
        
        for attempt in filteredAttempts {
            // Analyze release feeling errors
            if attempt.releaseFeeling == .early {
                errorCounts["Early Release"] = (errorCounts["Early Release"] ?? 0) + 1
            } else if attempt.releaseFeeling == .delayed {
                errorCounts["Delayed Release"] = (errorCounts["Delayed Release"] ?? 0) + 1
            }
            
            // Analyze rotation control errors
            if attempt.rotationControl == .fastButUncontrolled {
                errorCounts["Fast but Uncontrolled"] = (errorCounts["Fast but Uncontrolled"] ?? 0) + 1
            } else if attempt.rotationControl == .sluggish {
                errorCounts["Sluggish Rotation"] = (errorCounts["Sluggish Rotation"] ?? 0) + 1
            } else if attempt.rotationControl == .offTrack {
                errorCounts["Off Track"] = (errorCounts["Off Track"] ?? 0) + 1
            }
            
            // Analyze landing area errors
            if attempt.landingArea == .outOfBounds {
                errorCounts["Out of Bounds"] = (errorCounts["Out of Bounds"] ?? 0) + 1
            } else if attempt.landingArea == .leftSide {
                errorCounts["Landing Left"] = (errorCounts["Landing Left"] ?? 0) + 1
            } else if attempt.landingArea == .rightSide {
                errorCounts["Landing Right"] = (errorCounts["Landing Right"] ?? 0) + 1
            }
            
            // Analyze notes for common error keywords
            let notes = attempt.notes.lowercased()
            if notes.contains("early") || notes.contains("提前") {
                errorCounts["Early Release (Notes)"] = (errorCounts["Early Release (Notes)"] ?? 0) + 1
            }
            if notes.contains("late") || notes.contains("延迟") || notes.contains("晚") {
                errorCounts["Late Release (Notes)"] = (errorCounts["Late Release (Notes)"] ?? 0) + 1
            }
            if notes.contains("balance") || notes.contains("平衡") {
                errorCounts["Balance Issues"] = (errorCounts["Balance Issues"] ?? 0) + 1
            }
            if notes.contains("footwork") || notes.contains("步法") {
                errorCounts["Footwork Issues"] = (errorCounts["Footwork Issues"] ?? 0) + 1
            }
        }
        
        let totalErrors = errorCounts.values.reduce(0, +)
        
        return errorCounts.map { errorType, count in
            let percentage = totalErrors > 0 ? Double(count) / Double(totalErrors) * 100 : 0
            return ErrorTypeStats(errorType: errorType, count: count, percentage: percentage)
        }.sorted { $0.count > $1.count }
    }
    
    // MARK: - Technical Rating Trend
    
    func getTechnicalRatingTrend(period: StatsPeriod = .last30Days) -> [TrendData] {
        let attempts = AttemptLogManager.shared.attempts
        let filteredAttempts = filterAttemptsByPeriod(attempts, period: period)
        
        let groupedByDate = Dictionary(grouping: filteredAttempts) { attempt in
            Calendar.current.startOfDay(for: attempt.date)
        }
        
        return groupedByDate.map { date, dayAttempts in
            let averageRating = Double(dayAttempts.reduce(0) { $0 + $1.technicalRating }) / Double(dayAttempts.count)
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "MM/dd"
            
            return TrendData(
                period: dateFormatter.string(from: date),
                value: averageRating,
                date: date
            )
        }.sorted { $0.date < $1.date }
    }
    
    // MARK: - Helper Methods
    
    private func filterAttemptsByPeriod(_ attempts: [AttemptLog], period: StatsPeriod) -> [AttemptLog] {
        let now = Date()
        let calendar = Calendar.current
        
        switch period {
        case .last7Days:
            let startDate = calendar.date(byAdding: .day, value: -7, to: now) ?? now
            return attempts.filter { $0.date >= startDate }
        case .last30Days:
            let startDate = calendar.date(byAdding: .day, value: -30, to: now) ?? now
            return attempts.filter { $0.date >= startDate }
        case .last3Months:
            let startDate = calendar.date(byAdding: .month, value: -3, to: now) ?? now
            return attempts.filter { $0.date >= startDate }
        case .allTime:
            return attempts
        }
    }
}

// MARK: - Statistics Period Enum

enum StatsPeriod: String, CaseIterable {
    case last7Days = "Last 7 Days"
    case last30Days = "Last 30 Days"
    case last3Months = "Last 3 Months"
    case allTime = "All Time"
    
    var displayName: String {
        return self.rawValue
    }
}
