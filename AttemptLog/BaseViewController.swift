//
//  BaseViewController.swift
//  AttemptLog
//
//  Created by tyu<PERSON> on 2025/5/24.
//

import UIKit
import SnapKit

class BaseViewController: UIViewController {
    
    // MARK: - Properties
    
    private var gradientLayer: CAGradientLayer?
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupGradientBackground()
        setupNavigationBar()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        gradientLayer?.frame = view.bounds
    }
    
    // MARK: - Setup Methods
    
    private func setupGradientBackground() {
        gradientLayer = CAGradientLayer.primaryGradient()
        gradientLayer?.frame = view.bounds
        view.layer.insertSublayer(gradientLayer!, at: 0)
    }
    
    private func setupNavigationBar() {
        // Make navigation bar transparent to show gradient
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true
        navigationController?.navigationBar.tintColor = .lightText
        navigationController?.navigationBar.titleTextAttributes = [
            .foregroundColor: UIColor.lightText,
            .font: UIFont.systemFont(ofSize: 18, weight: .semibold)
        ]
        navigationController?.navigationBar.largeTitleTextAttributes = [
            .foregroundColor: UIColor.lightText,
            .font: UIFont.systemFont(ofSize: 32, weight: .bold)
        ]
    }
    
    // MARK: - Helper Methods
    
    /// Creates a card view with secondary gradient background and shadow
    func createCardView() -> UIView {
        let cardView = UIView()
        cardView.backgroundColor = .cardBackground
        cardView.layer.cornerRadius = 16
        cardView.layer.shadowColor = UIColor.cardShadow.cgColor
        cardView.layer.shadowOffset = CGSize(width: 0, height: 4)
        cardView.layer.shadowRadius = 8
        cardView.layer.shadowOpacity = 1.0
        return cardView
    }
    
    /// Creates a section header label
    func createSectionHeaderLabel(text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        label.textColor = .lightText
        return label
    }
    
    /// Creates a styled button with gradient background
    func createStyledButton(title: String, backgroundColor: UIColor = .accentBlue) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        button.backgroundColor = backgroundColor
        button.layer.cornerRadius = 12
        button.layer.shadowColor = backgroundColor.cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 2)
        button.layer.shadowRadius = 4
        button.layer.shadowOpacity = 0.3
        return button
    }
    
    /// Creates a styled text field
    func createStyledTextField(placeholder: String) -> UITextField {
        let textField = UITextField()
        textField.placeholder = placeholder
        textField.borderStyle = .none
        textField.backgroundColor = .cardBackground
        textField.layer.cornerRadius = 12
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor.systemGray4.cgColor
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.textColor = .primaryText
        
        // Add padding
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: textField.frame.height))
        textField.leftView = paddingView
        textField.leftViewMode = .always
        textField.rightView = paddingView
        textField.rightViewMode = .always
        
        return textField
    }
}
