//
//  AttemptsListViewController.swift
//  AttemptLog
//
//  Created by tyu<PERSON> on 2025/5/24.
//

import UIKit
import SnapKit

class AttemptsListViewController: BaseViewController {
    
    // MARK: - Properties
    
    private var attempts: [AttemptLog] = []
    
    // MARK: - UI Components
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(AttemptTableViewCell.self, forCellReuseIdentifier: AttemptTableViewCell.identifier)
        return tableView
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "target")
        imageView.tintColor = .lightText.withAlphaComponent(0.6)
        imageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = "No Attempts Yet"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = .lightText
        titleLabel.textAlignment = .center
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Start logging your discus throw attempts to track your progress"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = .lightText.withAlphaComponent(0.8)
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        
        view.addSubview(imageView)
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview()
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        return view
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadAttempts()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadAttempts()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "All Attempts"
        
        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(40)
        }
        
        updateEmptyState()
    }
    
    // MARK: - Data Methods
    
    private func loadAttempts() {
        attempts = AttemptLogManager.shared.attempts
        tableView.reloadData()
        updateEmptyState()
    }
    
    private func updateEmptyState() {
        emptyStateView.isHidden = !attempts.isEmpty
        tableView.isHidden = attempts.isEmpty
    }
}

// MARK: - UITableViewDataSource

extension AttemptsListViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return attempts.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: AttemptTableViewCell.identifier, for: indexPath) as! AttemptTableViewCell
        cell.configure(with: attempts[indexPath.row])
        return cell
    }
}

// MARK: - UITableViewDelegate

extension AttemptsListViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let attempt = attempts[indexPath.row]
        let detailVC = AttemptDetailViewController(attempt: attempt)
        navigationController?.pushViewController(detailVC, animated: true)
    }
    
    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            AttemptLogManager.shared.deleteAttempt(at: indexPath.row)
            attempts.remove(at: indexPath.row)
            tableView.deleteRows(at: [indexPath], with: .fade)
            updateEmptyState()
        }
    }
}

// MARK: - AttemptTableViewCell

class AttemptTableViewCell: UITableViewCell {
    static let identifier = "AttemptTableViewCell"
    
    private lazy var cardView: UIView = {
        let view = UIView()
        view.backgroundColor = .cardBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.cardShadow.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        view.layer.shadowOpacity = 1.0
        return view
    }()
    
    private lazy var dateLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.textColor = .secondaryText
        return label
    }()
    
    private lazy var successIndicator: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 6
        return view
    }()
    
    private lazy var ratingLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        label.textColor = .accentBlue
        return label
    }()
    
    private lazy var detailsLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.textColor = .primaryText
        label.numberOfLines = 2
        return label
    }()
    
    private lazy var notesLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textColor = .secondaryText
        label.numberOfLines = 1
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(cardView)
        cardView.addSubview(dateLabel)
        cardView.addSubview(successIndicator)
        cardView.addSubview(ratingLabel)
        cardView.addSubview(detailsLabel)
        cardView.addSubview(notesLabel)
        
        cardView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }
        
        dateLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        successIndicator.snp.makeConstraints { make in
            make.centerY.equalTo(dateLabel)
            make.trailing.equalToSuperview().offset(-16)
            make.width.height.equalTo(12)
        }
        
        ratingLabel.snp.makeConstraints { make in
            make.centerY.equalTo(dateLabel)
            make.trailing.equalTo(successIndicator.snp.leading).offset(-12)
        }
        
        detailsLabel.snp.makeConstraints { make in
            make.top.equalTo(dateLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        notesLabel.snp.makeConstraints { make in
            make.top.equalTo(detailsLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    func configure(with attempt: AttemptLog) {
        dateLabel.text = attempt.formattedDate
        ratingLabel.text = attempt.starRating
        
        successIndicator.backgroundColor = attempt.isSuccessful ? .accentGreen : .accentRed
        
        detailsLabel.text = "\(attempt.startingPosition.emoji) \(attempt.startingPosition.rawValue) • \(attempt.rotationControl.emoji) \(attempt.rotationControl.rawValue) • \(attempt.landingArea.emoji) \(attempt.landingArea.rawValue)"
        
        if attempt.notes.isEmpty {
            notesLabel.text = "No notes"
            notesLabel.textColor = .tertiaryLabel
        } else {
            notesLabel.text = attempt.notes
            notesLabel.textColor = .secondaryText
        }
    }
}
