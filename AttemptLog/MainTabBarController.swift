//
//  MainTabBarController.swift
//  AttemptLog
//
//  Created by tyuu on 2025/5/24.
//

import UIKit

class MainTabBarController: UITabBarController {

    override func viewDidLoad() {
        super.viewDidLoad()
        setupTabBar()
        setupViewControllers()
    }

    private func setupTabBar() {
        // Style the tab bar
        tabBar.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        tabBar.tintColor = .accentBlue
        tabBar.unselectedItemTintColor = .lightGray
        tabBar.isTranslucent = true

        // Add blur effect
        let blurEffect = UIBlurEffect(style: .dark)
        let blurView = UIVisualEffectView(effect: blurEffect)
        blurView.frame = tabBar.bounds
        blurView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        tabBar.insertSubview(blurView, at: 0)
    }

    private func setupViewControllers() {
        // First module: Throw Attempt Log
        let attemptLogVC = ThrowAttemptLogViewController()
        let attemptLogNav = UINavigationController(rootViewController: attemptLogVC)
        attemptLogNav.tabBarItem = UITabBarItem(
            title: "Attempt Log",
            image: UIImage(systemName: "target"),
            selectedImage: UIImage(systemName: "target.fill")
        )

        // Second module: Disruption Log
        let disruptionLogVC = DisruptionLogViewController()
        let disruptionLogNav = UINavigationController(rootViewController: disruptionLogVC)
        disruptionLogNav.tabBarItem = UITabBarItem(
            title: "Disruption Log",
            image: UIImage(systemName: "wind"),
            selectedImage: UIImage(systemName: "wind.circle.fill")
        )

        // Third module: Statistics Dashboard
        let statsDashboardVC = StatsDashboardViewController()
        let statsDashboardNav = UINavigationController(rootViewController: statsDashboardVC)
        statsDashboardNav.tabBarItem = UITabBarItem(
            title: "Statistics",
            image: UIImage(systemName: "chart.bar.fill"),
            selectedImage: UIImage(systemName: "chart.bar.fill")
        )

        // Placeholder for future modules
        let settingsVC = createPlaceholderViewController(
            title: "Settings",
            systemImageName: "gearshape.fill",
            message: "Settings coming soon..."
        )

        viewControllers = [attemptLogNav, disruptionLogNav, statsDashboardNav, settingsVC]
    }

    private func createPlaceholderViewController(title: String, systemImageName: String, message: String) -> UINavigationController {
        let placeholderVC = PlaceholderViewController(message: message)
        placeholderVC.title = title
        let navController = UINavigationController(rootViewController: placeholderVC)
        navController.tabBarItem = UITabBarItem(
            title: title,
            image: UIImage(systemName: systemImageName),
            selectedImage: nil
        )
        return navController
    }
}

// MARK: - Placeholder View Controller

class PlaceholderViewController: BaseViewController {

    private let message: String

    init(message: String) {
        self.message = message
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    private func setupUI() {
        let messageLabel = UILabel()
        messageLabel.text = message
        messageLabel.textColor = .lightText
        messageLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0

        view.addSubview(messageLabel)
        messageLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(40)
        }
    }
}
