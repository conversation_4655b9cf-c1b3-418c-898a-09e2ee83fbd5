//
//  AttemptLogModel.swift
//  AttemptLog
//
//  Created by tyu<PERSON> on 2025/5/24.
//

import Foundation

// MARK: - Enums for Attempt Log Fields

enum StartingPosition: String, CaseIterable, Codable {
    case standing = "Standing"
    case squatSpin = "Squat Spin"
    case lungeSpin = "Lunge Spin"

    var emoji: String {
        switch self {
        case .standing: return "🧍‍♂️"
        case .squatSpin: return "🏃‍♂️"
        case .lungeSpin: return "🤸‍♂️"
        }
    }
}

enum RotationControl: String, CaseIterable, Codable {
    case smooth = "Smooth"
    case fastButUncontrolled = "Fast but Uncontrolled"
    case sluggish = "Sluggish"
    case offTrack = "Off Track"

    var emoji: String {
        switch self {
        case .smooth: return "🔄"
        case .fastButUncontrolled: return "💨"
        case .sluggish: return "🐌"
        case .offTrack: return "↗️"
        }
    }
}

enum ReleaseFeeling: String, CaseIterable, Codable {
    case early = "Early"
    case appropriate = "Appropriate"
    case delayed = "Delayed"

    var emoji: String {
        switch self {
        case .early: return "⏰"
        case .appropriate: return "✅"
        case .delayed: return "⏳"
        }
    }
}

enum LandingArea: String, CaseIterable, Codable {
    case center = "Center"
    case leftSide = "Left Side"
    case rightSide = "Right Side"
    case outOfBounds = "Out of Bounds"

    var emoji: String {
        switch self {
        case .center: return "🎯"
        case .leftSide: return "⬅️"
        case .rightSide: return "➡️"
        case .outOfBounds: return "❌"
        }
    }
}

// MARK: - Attempt Log Model

struct AttemptLog: Codable {
    let id: UUID
    let date: Date
    let startingPosition: StartingPosition
    let rotationControl: RotationControl
    let releaseFeeling: ReleaseFeeling
    let landingArea: LandingArea
    let isSuccessful: Bool
    let technicalRating: Int // 1-5 stars
    let notes: String

    init(
        startingPosition: StartingPosition,
        rotationControl: RotationControl,
        releaseFeeling: ReleaseFeeling,
        landingArea: LandingArea,
        isSuccessful: Bool,
        technicalRating: Int,
        notes: String,
        date: Date = Date()
    ) {
        self.id = UUID()
        self.date = date
        self.startingPosition = startingPosition
        self.rotationControl = rotationControl
        self.releaseFeeling = releaseFeeling
        self.landingArea = landingArea
        self.isSuccessful = isSuccessful
        self.technicalRating = max(1, min(5, technicalRating)) // Ensure rating is between 1-5
        self.notes = notes
    }

    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    var starRating: String {
        return String(repeating: "⭐", count: technicalRating)
    }
}

// MARK: - Attempt Log Manager

class AttemptLogManager {
    static let shared = AttemptLogManager()
    private let userDefaults = UserDefaults.standard
    private let attemptsKey = "SavedAttempts"

    private init() {}

    var attempts: [AttemptLog] {
        get {
            guard let data = userDefaults.data(forKey: attemptsKey),
                  let attempts = try? JSONDecoder().decode([AttemptLog].self, from: data) else {
                return []
            }
            return attempts.sorted { $0.date > $1.date } // Most recent first
        }
        set {
            if let data = try? JSONEncoder().encode(newValue) {
                userDefaults.set(data, forKey: attemptsKey)
            }
        }
    }

    func addAttempt(_ attempt: AttemptLog) {
        var currentAttempts = attempts
        currentAttempts.append(attempt)
        attempts = currentAttempts
    }

    func deleteAttempt(at index: Int) {
        var currentAttempts = attempts
        guard index < currentAttempts.count else { return }
        currentAttempts.remove(at: index)
        attempts = currentAttempts
    }

    func getAttempt(by id: UUID) -> AttemptLog? {
        return attempts.first { $0.id == id }
    }

    var successRate: Double {
        let totalAttempts = attempts.count
        guard totalAttempts > 0 else { return 0.0 }
        let successfulAttempts = attempts.filter { $0.isSuccessful }.count
        return Double(successfulAttempts) / Double(totalAttempts)
    }

    var averageRating: Double {
        let totalAttempts = attempts.count
        guard totalAttempts > 0 else { return 0.0 }
        let totalRating = attempts.reduce(0) { $0 + $1.technicalRating }
        return Double(totalRating) / Double(totalAttempts)
    }
}

// MARK: - Disruption Log Enums

enum FieldCondition: String, CaseIterable, Codable {
    case flat = "Flat"
    case slippery = "Slippery"
    case dusty = "Dusty"
    case sticky = "Sticky"

    var emoji: String {
        switch self {
        case .flat: return "🏟️"
        case .slippery: return "🧊"
        case .dusty: return "🌪️"
        case .sticky: return "🍯"
        }
    }
}

enum WindCondition: String, CaseIterable, Codable {
    case tailwind = "Tailwind"
    case headwind = "Headwind"
    case crosswind = "Crosswind"
    case calm = "Calm"

    var emoji: String {
        switch self {
        case .tailwind: return "🌬️"
        case .headwind: return "💨"
        case .crosswind: return "🌀"
        case .calm: return "🍃"
        }
    }
}

enum EquipmentStatus: String, CaseIterable, Codable {
    case shoesGoodGrip = "Shoes Good Grip"
    case shoesSlipping = "Shoes Slipping"
    case discusChanged = "Discus Changed"
    case wearingGloves = "Wearing Gloves"
    case noGloves = "No Gloves"

    var emoji: String {
        switch self {
        case .shoesGoodGrip: return "👟"
        case .shoesSlipping: return "🦶"
        case .discusChanged: return "🥏"
        case .wearingGloves: return "🧤"
        case .noGloves: return "✋"
        }
    }
}

enum PhysicalState: String, CaseIterable, Codable {
    case fatigued = "Fatigued"
    case bloated = "Bloated"
    case nervous = "Nervous"
    case normal = "Normal"

    var emoji: String {
        switch self {
        case .fatigued: return "😴"
        case .bloated: return "🤰"
        case .nervous: return "😰"
        case .normal: return "😊"
        }
    }
}

enum TrainingRhythm: String, CaseIterable, Codable {
    case continuous = "Continuous Throws"
    case longWait = "Long Wait Between Turns"
    case interrupted = "Interrupted"

    var emoji: String {
        switch self {
        case .continuous: return "🔄"
        case .longWait: return "⏰"
        case .interrupted: return "⚠️"
        }
    }
}

// MARK: - Disruption Log Model

struct DisruptionLog: Codable {
    let id: UUID
    let date: Date
    let fieldCondition: FieldCondition
    let windCondition: WindCondition
    let equipmentStatus: [EquipmentStatus] // Multiple equipment factors can apply
    let physicalState: PhysicalState
    let trainingRhythm: TrainingRhythm
    let additionalNotes: String

    init(
        fieldCondition: FieldCondition,
        windCondition: WindCondition,
        equipmentStatus: [EquipmentStatus],
        physicalState: PhysicalState,
        trainingRhythm: TrainingRhythm,
        additionalNotes: String,
        date: Date = Date()
    ) {
        self.id = UUID()
        self.date = date
        self.fieldCondition = fieldCondition
        self.windCondition = windCondition
        self.equipmentStatus = equipmentStatus
        self.physicalState = physicalState
        self.trainingRhythm = trainingRhythm
        self.additionalNotes = additionalNotes
    }

    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    var equipmentStatusString: String {
        return equipmentStatus.map { "\($0.emoji) \($0.rawValue)" }.joined(separator: ", ")
    }
}

// MARK: - Disruption Log Manager

class DisruptionLogManager {
    static let shared = DisruptionLogManager()
    private let userDefaults = UserDefaults.standard
    private let disruptionsKey = "SavedDisruptions"

    private init() {}

    var disruptions: [DisruptionLog] {
        get {
            guard let data = userDefaults.data(forKey: disruptionsKey),
                  let disruptions = try? JSONDecoder().decode([DisruptionLog].self, from: data) else {
                return []
            }
            return disruptions.sorted { $0.date > $1.date } // Most recent first
        }
        set {
            if let data = try? JSONEncoder().encode(newValue) {
                userDefaults.set(data, forKey: disruptionsKey)
            }
        }
    }

    func addDisruption(_ disruption: DisruptionLog) {
        var currentDisruptions = disruptions
        currentDisruptions.append(disruption)
        disruptions = currentDisruptions
    }

    func deleteDisruption(at index: Int) {
        var currentDisruptions = disruptions
        guard index < currentDisruptions.count else { return }
        currentDisruptions.remove(at: index)
        disruptions = currentDisruptions
    }

    func getDisruption(by id: UUID) -> DisruptionLog? {
        return disruptions.first { $0.id == id }
    }

    // Analytics
    var mostCommonFieldCondition: FieldCondition? {
        let conditions = disruptions.map { $0.fieldCondition }
        return mostFrequent(in: conditions)
    }

    var mostCommonPhysicalState: PhysicalState? {
        let states = disruptions.map { $0.physicalState }
        return mostFrequent(in: states)
    }

    private func mostFrequent<T: Hashable>(in array: [T]) -> T? {
        let counts = Dictionary(grouping: array, by: { $0 }).mapValues { $0.count }
        return counts.max(by: { $0.value < $1.value })?.key
    }
}
