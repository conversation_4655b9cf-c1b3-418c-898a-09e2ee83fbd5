//
//  DisruptionDetailViewController.swift
//  AttemptLog
//
//  Created by tyuu on 2025/5/24.
//

import UIKit
import SnapKit

class DisruptionDetailViewController: BaseViewController {
    
    // MARK: - Properties
    
    private let disruption: DisruptionLog
    
    // MARK: - UI Components
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var contentView = UIView()
    
    private lazy var headerCardView: UIView = {
        let view = createCardView()
        return view
    }()
    
    private lazy var detailsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        return stackView
    }()
    
    // MARK: - Initialization
    
    init(disruption: DisruptionLog) {
        self.disruption = disruption
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "Disruption Details"
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(headerCardView)
        contentView.addSubview(detailsStackView)
        
        setupHeaderCard()
        setupDetailsCards()
        setupConstraints()
    }
    
    private func setupHeaderCard() {
        let dateLabel = UILabel()
        dateLabel.text = disruption.formattedDate
        dateLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        dateLabel.textColor = .primaryText
        dateLabel.textAlignment = .center
        
        let summaryView = createSummaryView()
        
        let headerStackView = UIStackView(arrangedSubviews: [dateLabel, summaryView])
        headerStackView.axis = .vertical
        headerStackView.spacing = 16
        headerStackView.alignment = .center
        
        headerCardView.addSubview(headerStackView)
        
        headerStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }
    }
    
    private func createSummaryView() -> UIView {
        let containerView = UIView()
        
        let iconView = UIView()
        iconView.backgroundColor = .accentOrange
        iconView.layer.cornerRadius = 25
        
        let iconLabel = UILabel()
        iconLabel.text = "🌪️"
        iconLabel.font = UIFont.systemFont(ofSize: 24)
        iconLabel.textAlignment = .center
        
        let statusLabel = UILabel()
        statusLabel.text = "Training Disruption Factors"
        statusLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        statusLabel.textColor = .accentOrange
        statusLabel.textAlignment = .center
        
        iconView.addSubview(iconLabel)
        containerView.addSubview(iconView)
        containerView.addSubview(statusLabel)
        
        iconView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(50)
        }
        
        iconLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(iconView.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        return containerView
    }
    
    private func setupDetailsCards() {
        // Field Condition
        let fieldCard = createDetailCard(
            title: "🔂 Field Condition",
            value: "\(disruption.fieldCondition.emoji) \(disruption.fieldCondition.rawValue)"
        )
        detailsStackView.addArrangedSubview(fieldCard)
        
        // Wind Condition
        let windCard = createDetailCard(
            title: "🌬️ Wind Condition",
            value: "\(disruption.windCondition.emoji) \(disruption.windCondition.rawValue)"
        )
        detailsStackView.addArrangedSubview(windCard)
        
        // Equipment Status
        if !disruption.equipmentStatus.isEmpty {
            let equipmentCard = createDetailCard(
                title: "👟 Equipment Status",
                value: disruption.equipmentStatusString
            )
            detailsStackView.addArrangedSubview(equipmentCard)
        }
        
        // Physical State
        let physicalCard = createDetailCard(
            title: "😵‍💫 Physical State",
            value: "\(disruption.physicalState.emoji) \(disruption.physicalState.rawValue)"
        )
        detailsStackView.addArrangedSubview(physicalCard)
        
        // Training Rhythm
        let rhythmCard = createDetailCard(
            title: "🕐 Training Rhythm",
            value: "\(disruption.trainingRhythm.emoji) \(disruption.trainingRhythm.rawValue)"
        )
        detailsStackView.addArrangedSubview(rhythmCard)
        
        // Additional Notes
        if !disruption.additionalNotes.isEmpty {
            let notesCard = createNotesCard()
            detailsStackView.addArrangedSubview(notesCard)
        }
    }
    
    private func createDetailCard(title: String, value: String) -> UIView {
        let cardView = createCardView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = .primaryText
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        valueLabel.textColor = .accentOrange
        valueLabel.numberOfLines = 0
        valueLabel.textAlignment = .right
        
        cardView.addSubview(titleLabel)
        cardView.addSubview(valueLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview().inset(16)
            make.width.lessThanOrEqualTo(150)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.trailing.centerY.equalToSuperview().inset(16)
            make.leading.greaterThanOrEqualTo(titleLabel.snp.trailing).offset(8)
            make.top.bottom.equalToSuperview().inset(16)
        }
        
        return cardView
    }
    
    private func createNotesCard() -> UIView {
        let cardView = createCardView()
        
        let titleLabel = UILabel()
        titleLabel.text = "📝 Additional Notes"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = .primaryText
        
        let notesLabel = UILabel()
        notesLabel.text = disruption.additionalNotes
        notesLabel.font = UIFont.systemFont(ofSize: 16)
        notesLabel.textColor = .secondaryText
        notesLabel.numberOfLines = 0
        
        cardView.addSubview(titleLabel)
        cardView.addSubview(notesLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        notesLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
        
        return cardView
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        headerCardView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        detailsStackView.snp.makeConstraints { make in
            make.top.equalTo(headerCardView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
}
