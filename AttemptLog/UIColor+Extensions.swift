//
//  UIColor+Extensions.swift
//  AttemptLog
//
//  Created by tyu<PERSON> on 2025/5/24.
//

import UIKit

extension UIColor {
    
    // MARK: - App Theme Colors
    
    /// Primary gradient colors for the app background
    static let primaryGradientStart = UIColor(red: 0.2, green: 0.4, blue: 0.8, alpha: 1.0) // Deep blue
    static let primaryGradientEnd = UIColor(red: 0.1, green: 0.2, blue: 0.5, alpha: 1.0)   // Darker blue
    
    /// Secondary gradient colors for cards and components
    static let secondaryGradientStart = UIColor(red: 0.95, green: 0.95, blue: 0.97, alpha: 1.0) // Light gray
    static let secondaryGradientEnd = UIColor(red: 0.88, green: 0.88, blue: 0.92, alpha: 1.0)   // Slightly darker gray
    
    /// Accent colors
    static let accentBlue = UIColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0)
    static let accentGreen = UIColor(red: 0.2, green: 0.78, blue: 0.35, alpha: 1.0)
    static let accentOrange = UIColor(red: 1.0, green: 0.58, blue: 0.0, alpha: 1.0)
    static let accentRed = UIColor(red: 1.0, green: 0.23, blue: 0.19, alpha: 1.0)
    
    /// Text colors
    static let primaryText = UIColor.label
    static let secondaryText = UIColor.secondaryLabel
    static let lightText = UIColor.white
    
    /// Card and component colors
    static let cardBackground = UIColor.systemBackground
    static let cardShadow = UIColor.black.withAlphaComponent(0.1)
    
    // MARK: - Convenience Initializers
    
    convenience init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            alpha: Double(a) / 255
        )
    }
}

// MARK: - Gradient Helper

extension CAGradientLayer {
    
    /// Creates a primary gradient layer for the app background
    static func primaryGradient() -> CAGradientLayer {
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor.primaryGradientStart.cgColor,
            UIColor.primaryGradientEnd.cgColor
        ]
        gradient.startPoint = CGPoint(x: 0, y: 0)
        gradient.endPoint = CGPoint(x: 1, y: 1)
        return gradient
    }
    
    /// Creates a secondary gradient layer for cards and components
    static func secondaryGradient() -> CAGradientLayer {
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor.secondaryGradientStart.cgColor,
            UIColor.secondaryGradientEnd.cgColor
        ]
        gradient.startPoint = CGPoint(x: 0, y: 0)
        gradient.endPoint = CGPoint(x: 1, y: 1)
        return gradient
    }
}
