//
//  DisruptionLogViewController.swift
//  AttemptLog
//
//  Created by tyuu on 2025/5/24.
//

import UIKit
import SnapKit

class DisruptionLogViewController: BaseViewController {
    
    // MARK: - UI Components
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var contentView = UIView()
    
    private lazy var headerView: UIView = {
        let view = createCardView()
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "🌪️ Training Disruption Log"
        label.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        label.textColor = .primaryText
        label.textAlignment = .center
        return label
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Record environmental and physical factors affecting your training"
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = .secondaryText
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var formCardView: UIView = {
        let view = createCardView()
        return view
    }()
    
    private lazy var addButton: UIButton = {
        let button = createStyledButton(title: "📝 Log Disruption Factors", backgroundColor: .accentOrange)
        button.addTarget(self, action: #selector(addButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var viewDisruptionsButton: UIButton = {
        let button = createStyledButton(title: "📊 View All Disruptions", backgroundColor: .accentBlue)
        button.addTarget(self, action: #selector(viewDisruptionsButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var statsCardView: UIView = {
        let view = createCardView()
        return view
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        updateStats()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateStats()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "Disruption Log"
        navigationController?.navigationBar.prefersLargeTitles = true
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        setupHeaderView()
        setupFormCard()
        setupStatsCard()
        setupConstraints()
    }
    
    private func setupHeaderView() {
        headerView.addSubview(titleLabel)
        headerView.addSubview(subtitleLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func setupFormCard() {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.distribution = .fillEqually
        
        stackView.addArrangedSubview(addButton)
        stackView.addArrangedSubview(viewDisruptionsButton)
        
        formCardView.addSubview(stackView)
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }
        
        addButton.snp.makeConstraints { make in
            make.height.equalTo(50)
        }
        
        viewDisruptionsButton.snp.makeConstraints { make in
            make.height.equalTo(50)
        }
    }
    
    private func setupStatsCard() {
        let statsLabel = createSectionHeaderLabel(text: "📈 Disruption Analysis")
        statsLabel.textColor = .primaryText
        
        statsCardView.addSubview(statsLabel)
        
        statsLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(20)
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        contentView.addSubview(headerView)
        contentView.addSubview(formCardView)
        contentView.addSubview(statsCardView)
        
        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        formCardView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        statsCardView.snp.makeConstraints { make in
            make.top.equalTo(formCardView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
            make.height.greaterThanOrEqualTo(120)
        }
    }
    
    // MARK: - Actions
    
    @objc private func addButtonTapped() {
        let addDisruptionVC = AddDisruptionViewController()
        let navController = UINavigationController(rootViewController: addDisruptionVC)
        present(navController, animated: true)
    }
    
    @objc private func viewDisruptionsButtonTapped() {
        let disruptionsListVC = DisruptionsListViewController()
        navigationController?.pushViewController(disruptionsListVC, animated: true)
    }
    
    // MARK: - Helper Methods
    
    private func updateStats() {
        // Remove existing stats views
        statsCardView.subviews.forEach { view in
            if view.tag == 999 {
                view.removeFromSuperview()
            }
        }
        
        let manager = DisruptionLogManager.shared
        let disruptions = manager.disruptions
        
        let statsStackView = UIStackView()
        statsStackView.axis = .vertical
        statsStackView.spacing = 12
        statsStackView.tag = 999
        
        // Total disruptions
        let totalLabel = createStatLabel(title: "Total Records", value: "\(disruptions.count)")
        statsStackView.addArrangedSubview(totalLabel)
        
        if !disruptions.isEmpty {
            // Most common field condition
            if let commonField = manager.mostCommonFieldCondition {
                let fieldLabel = createStatLabel(title: "Common Field Issue", value: "\(commonField.emoji) \(commonField.rawValue)")
                statsStackView.addArrangedSubview(fieldLabel)
            }
            
            // Most common physical state
            if let commonState = manager.mostCommonPhysicalState {
                let stateLabel = createStatLabel(title: "Common Physical State", value: "\(commonState.emoji) \(commonState.rawValue)")
                statsStackView.addArrangedSubview(stateLabel)
            }
        }
        
        statsCardView.addSubview(statsStackView)
        
        statsStackView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(60)
            make.leading.trailing.bottom.equalToSuperview().inset(20)
        }
    }
    
    private func createStatLabel(title: String, value: String) -> UIView {
        let containerView = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryText
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        valueLabel.textColor = .accentOrange
        valueLabel.numberOfLines = 0
        
        containerView.addSubview(titleLabel)
        containerView.addSubview(valueLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.leading.top.bottom.equalToSuperview()
            make.width.lessThanOrEqualTo(120)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.trailing.top.bottom.equalToSuperview()
            make.leading.greaterThanOrEqualTo(titleLabel.snp.trailing).offset(8)
        }
        
        return containerView
    }
}
