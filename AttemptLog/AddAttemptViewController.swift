//
//  AddAttemptViewController.swift
//  AttemptLog
//
//  Created by tyu<PERSON> on 2025/5/24.
//

import UIKit
import SnapKit

class AddAttemptViewController: BaseViewController {
    
    // MARK: - Properties
    
    private var selectedStartingPosition: StartingPosition = .standing
    private var selectedRotationControl: RotationControl = .smooth
    private var selectedReleaseFeeling: ReleaseFeeling = .appropriate
    private var selectedLandingArea: LandingArea = .center
    private var selectedRating: Int = 3
    private var isSuccessful: Bool = true
    
    // MARK: - UI Components
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var contentView = UIView()
    
    private lazy var formStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 20
        return stackView
    }()
    
    private lazy var notesTextView: UITextView = {
        let textView = UITextView()
        textView.backgroundColor = .cardBackground
        textView.layer.cornerRadius = 12
        textView.layer.borderWidth = 1
        textView.layer.borderColor = UIColor.systemGray4.cgColor
        textView.font = UIFont.systemFont(ofSize: 16)
        textView.textColor = .primaryText
        textView.text = "Add your notes here..."
        textView.textColor = .placeholderText
        textView.delegate = self
        return textView
    }()
    
    private lazy var successSwitch: UISwitch = {
        let switchControl = UISwitch()
        switchControl.isOn = true
        switchControl.onTintColor = .accentGreen
        switchControl.addTarget(self, action: #selector(successSwitchChanged), for: .valueChanged)
        return switchControl
    }()
    
    private lazy var ratingStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 8
        stackView.distribution = .fillEqually
        return stackView
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
    }
    
    // MARK: - Setup Methods
    
    private func setupNavigationBar() {
        title = "New Attempt"
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveTapped)
        )
    }
    
    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(formStackView)
        
        setupFormSections()
        setupConstraints()
    }
    
    private func setupFormSections() {
        // Date section
        let dateCard = createSectionCard(title: "📅 Date", content: createDateSection())
        formStackView.addArrangedSubview(dateCard)
        
        // Starting position section
        let positionCard = createSectionCard(title: "🧍‍♂️ Starting Position", content: createStartingPositionSection())
        formStackView.addArrangedSubview(positionCard)
        
        // Rotation control section
        let rotationCard = createSectionCard(title: "🔁 Rotation Control", content: createRotationControlSection())
        formStackView.addArrangedSubview(rotationCard)
        
        // Release feeling section
        let releaseCard = createSectionCard(title: "💨 Release Feeling", content: createReleaseFeelingSection())
        formStackView.addArrangedSubview(releaseCard)
        
        // Landing area section
        let landingCard = createSectionCard(title: "🧱 Landing Area", content: createLandingAreaSection())
        formStackView.addArrangedSubview(landingCard)
        
        // Success section
        let successCard = createSectionCard(title: "✅ Success", content: createSuccessSection())
        formStackView.addArrangedSubview(successCard)
        
        // Rating section
        let ratingCard = createSectionCard(title: "⭐ Technical Rating", content: createRatingSection())
        formStackView.addArrangedSubview(ratingCard)
        
        // Notes section
        let notesCard = createSectionCard(title: "📝 Notes", content: createNotesSection())
        formStackView.addArrangedSubview(notesCard)
    }
    
    private func createSectionCard(title: String, content: UIView) -> UIView {
        let cardView = createCardView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = .primaryText
        
        cardView.addSubview(titleLabel)
        cardView.addSubview(content)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        content.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
        
        return cardView
    }
    
    private func createDateSection() -> UIView {
        let label = UILabel()
        label.text = DateFormatter.localizedString(from: Date(), dateStyle: .full, timeStyle: .short)
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = .secondaryText
        return label
    }
    
    private func createStartingPositionSection() -> UIView {
        return createSegmentedControl(
            items: StartingPosition.allCases.map { "\($0.emoji) \($0.rawValue)" },
            selectedIndex: 0,
            action: #selector(startingPositionChanged(_:))
        )
    }
    
    private func createRotationControlSection() -> UIView {
        return createSegmentedControl(
            items: RotationControl.allCases.map { "\($0.emoji) \($0.rawValue)" },
            selectedIndex: 0,
            action: #selector(rotationControlChanged(_:))
        )
    }
    
    private func createReleaseFeelingSection() -> UIView {
        return createSegmentedControl(
            items: ReleaseFeeling.allCases.map { "\($0.emoji) \($0.rawValue)" },
            selectedIndex: 1,
            action: #selector(releaseFeelingChanged(_:))
        )
    }
    
    private func createLandingAreaSection() -> UIView {
        return createSegmentedControl(
            items: LandingArea.allCases.map { "\($0.emoji) \($0.rawValue)" },
            selectedIndex: 0,
            action: #selector(landingAreaChanged(_:))
        )
    }
    
    private func createSuccessSection() -> UIView {
        let containerView = UIView()
        
        let label = UILabel()
        label.text = "Successful attempt"
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = .primaryText
        
        containerView.addSubview(label)
        containerView.addSubview(successSwitch)
        
        label.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
        }
        
        successSwitch.snp.makeConstraints { make in
            make.trailing.centerY.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }
        
        return containerView
    }
    
    private func createRatingSection() -> UIView {
        setupRatingButtons()
        return ratingStackView
    }
    
    private func createNotesSection() -> UIView {
        notesTextView.snp.makeConstraints { make in
            make.height.equalTo(100)
        }
        return notesTextView
    }
    
    private func createSegmentedControl(items: [String], selectedIndex: Int, action: Selector) -> UIView {
        let segmentedControl = UISegmentedControl(items: items)
        segmentedControl.selectedSegmentIndex = selectedIndex
        segmentedControl.addTarget(self, action: action, for: .valueChanged)
        segmentedControl.backgroundColor = .systemGray6
        segmentedControl.selectedSegmentTintColor = .accentBlue
        segmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        return segmentedControl
    }
    
    private func setupRatingButtons() {
        ratingStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        for i in 1...5 {
            let button = UIButton(type: .system)
            button.setTitle("⭐", for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 24)
            button.tag = i
            button.addTarget(self, action: #selector(ratingButtonTapped(_:)), for: .touchUpInside)
            updateRatingButton(button, isSelected: i <= selectedRating)
            ratingStackView.addArrangedSubview(button)
        }
    }
    
    private func updateRatingButton(_ button: UIButton, isSelected: Bool) {
        button.alpha = isSelected ? 1.0 : 0.3
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        formStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }
    }
    
    // MARK: - Actions
    
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }
    
    @objc private func saveTapped() {
        let notes = notesTextView.textColor == .placeholderText ? "" : notesTextView.text ?? ""
        
        let attempt = AttemptLog(
            startingPosition: selectedStartingPosition,
            rotationControl: selectedRotationControl,
            releaseFeeling: selectedReleaseFeeling,
            landingArea: selectedLandingArea,
            isSuccessful: isSuccessful,
            technicalRating: selectedRating,
            notes: notes
        )
        
        AttemptLogManager.shared.addAttempt(attempt)
        
        dismiss(animated: true)
    }
    
    @objc private func startingPositionChanged(_ sender: UISegmentedControl) {
        selectedStartingPosition = StartingPosition.allCases[sender.selectedSegmentIndex]
    }
    
    @objc private func rotationControlChanged(_ sender: UISegmentedControl) {
        selectedRotationControl = RotationControl.allCases[sender.selectedSegmentIndex]
    }
    
    @objc private func releaseFeelingChanged(_ sender: UISegmentedControl) {
        selectedReleaseFeeling = ReleaseFeeling.allCases[sender.selectedSegmentIndex]
    }
    
    @objc private func landingAreaChanged(_ sender: UISegmentedControl) {
        selectedLandingArea = LandingArea.allCases[sender.selectedSegmentIndex]
    }
    
    @objc private func successSwitchChanged(_ sender: UISwitch) {
        isSuccessful = sender.isOn
    }
    
    @objc private func ratingButtonTapped(_ sender: UIButton) {
        selectedRating = sender.tag
        setupRatingButtons()
    }
}

// MARK: - UITextViewDelegate

extension AddAttemptViewController: UITextViewDelegate {
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.textColor == .placeholderText {
            textView.text = ""
            textView.textColor = .primaryText
        }
    }
    
    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text.isEmpty {
            textView.text = "Add your notes here..."
            textView.textColor = .placeholderText
        }
    }
}
