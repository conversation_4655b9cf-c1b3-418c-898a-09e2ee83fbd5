//
//  AttemptDetailViewController.swift
//  AttemptLog
//
//  Created by tyuu on 2025/5/24.
//

import UIKit
import SnapKit

class AttemptDetailViewController: BaseViewController {
    
    // MARK: - Properties
    
    private let attempt: AttemptLog
    
    // MARK: - UI Components
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var contentView = UIView()
    
    private lazy var headerCardView: UIView = {
        let view = createCardView()
        return view
    }()
    
    private lazy var detailsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        return stackView
    }()
    
    // MARK: - Initialization
    
    init(attempt: AttemptLog) {
        self.attempt = attempt
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "Attempt Details"
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(headerCardView)
        contentView.addSubview(detailsStackView)
        
        setupHeaderCard()
        setupDetailsCards()
        setupConstraints()
    }
    
    private func setupHeaderCard() {
        let dateLabel = UILabel()
        dateLabel.text = attempt.formattedDate
        dateLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        dateLabel.textColor = .primaryText
        dateLabel.textAlignment = .center
        
        let successView = createSuccessIndicatorView()
        let ratingView = createRatingView()
        
        let headerStackView = UIStackView(arrangedSubviews: [dateLabel, successView, ratingView])
        headerStackView.axis = .vertical
        headerStackView.spacing = 16
        headerStackView.alignment = .center
        
        headerCardView.addSubview(headerStackView)
        
        headerStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }
    }
    
    private func createSuccessIndicatorView() -> UIView {
        let containerView = UIView()
        
        let iconView = UIView()
        iconView.backgroundColor = attempt.isSuccessful ? .accentGreen : .accentRed
        iconView.layer.cornerRadius = 25
        
        let iconLabel = UILabel()
        iconLabel.text = attempt.isSuccessful ? "✓" : "✗"
        iconLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        iconLabel.textColor = .white
        iconLabel.textAlignment = .center
        
        let statusLabel = UILabel()
        statusLabel.text = attempt.isSuccessful ? "Successful Attempt" : "Failed Attempt"
        statusLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        statusLabel.textColor = attempt.isSuccessful ? .accentGreen : .accentRed
        statusLabel.textAlignment = .center
        
        iconView.addSubview(iconLabel)
        containerView.addSubview(iconView)
        containerView.addSubview(statusLabel)
        
        iconView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(50)
        }
        
        iconLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(iconView.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        return containerView
    }
    
    private func createRatingView() -> UIView {
        let containerView = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = "Technical Rating"
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryText
        titleLabel.textAlignment = .center
        
        let ratingLabel = UILabel()
        ratingLabel.text = attempt.starRating
        ratingLabel.font = UIFont.systemFont(ofSize: 24)
        ratingLabel.textAlignment = .center
        
        containerView.addSubview(titleLabel)
        containerView.addSubview(ratingLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }
        
        ratingLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        return containerView
    }
    
    private func setupDetailsCards() {
        // Starting Position
        let positionCard = createDetailCard(
            title: "🧍‍♂️ Starting Position",
            value: "\(attempt.startingPosition.emoji) \(attempt.startingPosition.rawValue)"
        )
        detailsStackView.addArrangedSubview(positionCard)
        
        // Rotation Control
        let rotationCard = createDetailCard(
            title: "🔁 Rotation Control",
            value: "\(attempt.rotationControl.emoji) \(attempt.rotationControl.rawValue)"
        )
        detailsStackView.addArrangedSubview(rotationCard)
        
        // Release Feeling
        let releaseCard = createDetailCard(
            title: "💨 Release Feeling",
            value: "\(attempt.releaseFeeling.emoji) \(attempt.releaseFeeling.rawValue)"
        )
        detailsStackView.addArrangedSubview(releaseCard)
        
        // Landing Area
        let landingCard = createDetailCard(
            title: "🧱 Landing Area",
            value: "\(attempt.landingArea.emoji) \(attempt.landingArea.rawValue)"
        )
        detailsStackView.addArrangedSubview(landingCard)
        
        // Notes
        if !attempt.notes.isEmpty {
            let notesCard = createNotesCard()
            detailsStackView.addArrangedSubview(notesCard)
        }
    }
    
    private func createDetailCard(title: String, value: String) -> UIView {
        let cardView = createCardView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = .primaryText
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        valueLabel.textColor = .accentBlue
        valueLabel.textAlignment = .right
        
        cardView.addSubview(titleLabel)
        cardView.addSubview(valueLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview().inset(16)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.trailing.centerY.equalToSuperview().inset(16)
            make.leading.greaterThanOrEqualTo(titleLabel.snp.trailing).offset(8)
            make.top.bottom.equalToSuperview().inset(16)
        }
        
        return cardView
    }
    
    private func createNotesCard() -> UIView {
        let cardView = createCardView()
        
        let titleLabel = UILabel()
        titleLabel.text = "📝 Notes"
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = .primaryText
        
        let notesLabel = UILabel()
        notesLabel.text = attempt.notes
        notesLabel.font = UIFont.systemFont(ofSize: 16)
        notesLabel.textColor = .secondaryText
        notesLabel.numberOfLines = 0
        
        cardView.addSubview(titleLabel)
        cardView.addSubview(notesLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        notesLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
        
        return cardView
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        headerCardView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        detailsStackView.snp.makeConstraints { make in
            make.top.equalTo(headerCardView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
}
