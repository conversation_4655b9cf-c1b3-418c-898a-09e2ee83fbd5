//
//  ThrowAttemptLogViewController.swift
//  AttemptLog
//
//  Created by tyu<PERSON> on 2025/5/24.
//

import UIKit
import SnapKit

class ThrowAttemptLogViewController: BaseViewController {
    
    // MARK: - UI Components
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var contentView = UIView()
    
    private lazy var headerView: UIView = {
        let view = createCardView()
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "🥏 Discus Throw Attempt Log"
        label.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        label.textColor = .primaryText
        label.textAlignment = .center
        return label
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Record your throwing technique and results"
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = .secondaryText
        label.textAlignment = .center
        return label
    }()
    
    private lazy var formCardView: UIView = {
        let view = createCardView()
        return view
    }()
    
    private lazy var addButton: UIButton = {
        let button = createStyledButton(title: "📝 Log New Attempt", backgroundColor: .accentGreen)
        button.addTarget(self, action: #selector(addButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var viewAttemptsButton: UIButton = {
        let button = createStyledButton(title: "📊 View All Attempts", backgroundColor: .accentBlue)
        button.addTarget(self, action: #selector(viewAttemptsButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var statsCardView: UIView = {
        let view = createCardView()
        return view
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        updateStats()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateStats()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "Attempt Log"
        navigationController?.navigationBar.prefersLargeTitles = true
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        setupHeaderView()
        setupFormCard()
        setupStatsCard()
        setupConstraints()
    }
    
    private func setupHeaderView() {
        headerView.addSubview(titleLabel)
        headerView.addSubview(subtitleLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func setupFormCard() {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.distribution = .fillEqually
        
        stackView.addArrangedSubview(addButton)
        stackView.addArrangedSubview(viewAttemptsButton)
        
        formCardView.addSubview(stackView)
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }
        
        addButton.snp.makeConstraints { make in
            make.height.equalTo(50)
        }
        
        viewAttemptsButton.snp.makeConstraints { make in
            make.height.equalTo(50)
        }
    }
    
    private func setupStatsCard() {
        let statsLabel = createSectionHeaderLabel(text: "📈 Quick Stats")
        statsLabel.textColor = .primaryText
        
        statsCardView.addSubview(statsLabel)
        
        statsLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(20)
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        contentView.addSubview(headerView)
        contentView.addSubview(formCardView)
        contentView.addSubview(statsCardView)
        
        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        formCardView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        statsCardView.snp.makeConstraints { make in
            make.top.equalTo(formCardView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
            make.height.greaterThanOrEqualTo(120)
        }
    }
    
    // MARK: - Actions
    
    @objc private func addButtonTapped() {
        let addAttemptVC = AddAttemptViewController()
        let navController = UINavigationController(rootViewController: addAttemptVC)
        present(navController, animated: true)
    }
    
    @objc private func viewAttemptsButtonTapped() {
        let attemptsListVC = AttemptsListViewController()
        navigationController?.pushViewController(attemptsListVC, animated: true)
    }
    
    // MARK: - Helper Methods
    
    private func updateStats() {
        // Remove existing stats views
        statsCardView.subviews.forEach { view in
            if view.tag == 999 {
                view.removeFromSuperview()
            }
        }
        
        let manager = AttemptLogManager.shared
        let attempts = manager.attempts
        
        let statsStackView = UIStackView()
        statsStackView.axis = .vertical
        statsStackView.spacing = 12
        statsStackView.tag = 999
        
        // Total attempts
        let totalLabel = createStatLabel(title: "Total Attempts", value: "\(attempts.count)")
        statsStackView.addArrangedSubview(totalLabel)
        
        if !attempts.isEmpty {
            // Success rate
            let successRate = manager.successRate * 100
            let successLabel = createStatLabel(title: "Success Rate", value: String(format: "%.1f%%", successRate))
            statsStackView.addArrangedSubview(successLabel)
            
            // Average rating
            let avgRating = manager.averageRating
            let ratingLabel = createStatLabel(title: "Average Rating", value: String(format: "%.1f ⭐", avgRating))
            statsStackView.addArrangedSubview(ratingLabel)
        }
        
        statsCardView.addSubview(statsStackView)
        
        statsStackView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(60)
            make.leading.trailing.bottom.equalToSuperview().inset(20)
        }
    }
    
    private func createStatLabel(title: String, value: String) -> UIView {
        let containerView = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .secondaryText
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        valueLabel.textColor = .accentBlue
        
        containerView.addSubview(titleLabel)
        containerView.addSubview(valueLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.leading.top.bottom.equalToSuperview()
        }
        
        valueLabel.snp.makeConstraints { make in
            make.trailing.top.bottom.equalToSuperview()
            make.leading.greaterThanOrEqualTo(titleLabel.snp.trailing).offset(8)
        }
        
        return containerView
    }
}
