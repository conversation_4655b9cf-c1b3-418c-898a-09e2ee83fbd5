//
//  StatsDashboardViewController.swift
//  AttemptLog
//
//  Created by tyu<PERSON> on 2025/5/24.
//

import UIKit
import SnapKit

class StatsDashboardViewController: BaseViewController {

    // MARK: - Properties

    private var selectedPeriod: StatsPeriod = .last30Days

    // MARK: - UI Components

    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()

    private lazy var contentView = UIView()

    private lazy var headerView: UIView = {
        let view = createCardView()
        return view
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "📊 Throw Stats Dashboard"
        label.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        label.textColor = .primaryText
        label.textAlignment = .center
        return label
    }()

    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Visualize your throwing performance and progress"
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = .secondaryText
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var periodSelector: UISegmentedControl = {
        let items = StatsPeriod.allCases.map { $0.displayName }
        let segmentedControl = UISegmentedControl(items: items)
        segmentedControl.selectedSegmentIndex = 1 // Default to last 30 days
        segmentedControl.backgroundColor = .systemGray6
        segmentedControl.selectedSegmentTintColor = .systemPurple
        segmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        segmentedControl.addTarget(self, action: #selector(periodChanged(_:)), for: .valueChanged)
        return segmentedControl
    }()

    private lazy var statsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 20
        return stackView
    }()

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadStatistics()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadStatistics()
    }

    // MARK: - Setup Methods

    private func setupUI() {
        title = "Statistics"
        navigationController?.navigationBar.prefersLargeTitles = true

        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        setupHeaderView()
        setupConstraints()
    }

    private func setupHeaderView() {
        headerView.addSubview(titleLabel)
        headerView.addSubview(subtitleLabel)
        headerView.addSubview(periodSelector)

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        periodSelector.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
            make.height.equalTo(32)
        }
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        contentView.addSubview(headerView)
        contentView.addSubview(statsStackView)

        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        statsStackView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    // MARK: - Actions

    @objc private func periodChanged(_ sender: UISegmentedControl) {
        selectedPeriod = StatsPeriod.allCases[sender.selectedSegmentIndex]
        loadStatistics()
    }

    // MARK: - Data Loading

    private func loadStatistics() {
        // Clear existing stats
        statsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        let analyzer = StatsAnalyzer.shared

        // Check if we have data
        let attempts = AttemptLogManager.shared.attempts
        if attempts.isEmpty {
            showEmptyState()
            return
        }

        // Success Rate Trend
        let successRateCard = createSuccessRateCard()
        statsStackView.addArrangedSubview(successRateCard)

        // Landing Distribution
        let landingDistributionCard = createLandingDistributionCard()
        statsStackView.addArrangedSubview(landingDistributionCard)

        // Top Attempts
        let topAttemptsCard = createTopAttemptsCard()
        statsStackView.addArrangedSubview(topAttemptsCard)

        // Error Type Statistics
        let errorStatsCard = createErrorStatsCard()
        statsStackView.addArrangedSubview(errorStatsCard)

        // Technical Rating Trend
        let ratingTrendCard = createRatingTrendCard()
        statsStackView.addArrangedSubview(ratingTrendCard)
    }

    private func showEmptyState() {
        let emptyStateView = createEmptyStateView()
        statsStackView.addArrangedSubview(emptyStateView)
    }

    private func createEmptyStateView() -> UIView {
        let cardView = createCardView()

        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chart.bar.doc.horizontal")
        imageView.tintColor = .systemPurple.withAlphaComponent(0.6)
        imageView.contentMode = .scaleAspectFit

        let titleLabel = UILabel()
        titleLabel.text = "No Data Available"
        titleLabel.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        titleLabel.textColor = .primaryText
        titleLabel.textAlignment = .center

        let messageLabel = UILabel()
        messageLabel.text = "Start logging your throw attempts to see detailed statistics and trends"
        messageLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        messageLabel.textColor = .secondaryText
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0

        cardView.addSubview(imageView)
        cardView.addSubview(titleLabel)
        cardView.addSubview(messageLabel)

        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(40)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(60)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        messageLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-40)
        }

        return cardView
    }

    private func createSuccessRateCard() -> UIView {
        let cardView = createCardView()

        let titleLabel = UILabel()
        titleLabel.text = "📈 Success Rate Trend"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = .primaryText

        let analyzer = StatsAnalyzer.shared
        let successRateData = analyzer.getSuccessRateTrend(period: selectedPeriod)

        let chartView = createSuccessRateChartView(data: successRateData)

        cardView.addSubview(titleLabel)
        cardView.addSubview(chartView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        chartView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(200)
        }

        return cardView
    }

    private func createSuccessRateChartView(data: [SuccessRateData]) -> UIView {
        let chartView = UIView()
        chartView.backgroundColor = .systemGray6
        chartView.layer.cornerRadius = 8

        if data.isEmpty {
            let noDataLabel = UILabel()
            noDataLabel.text = "No data for selected period"
            noDataLabel.textColor = .secondaryText
            noDataLabel.textAlignment = .center
            noDataLabel.font = UIFont.systemFont(ofSize: 14)

            chartView.addSubview(noDataLabel)
            noDataLabel.snp.makeConstraints { make in
                make.center.equalToSuperview()
            }
            return chartView
        }

        // Simple text-based chart for now
        let statsLabel = UILabel()
        let averageSuccessRate = data.reduce(0) { $0 + $1.successRate } / Double(data.count)
        let totalAttempts = data.reduce(0) { $0 + $1.totalAttempts }
        let totalSuccessful = data.reduce(0) { $0 + $1.successfulAttempts }

        statsLabel.text = """
        Average Success Rate: \(String(format: "%.1f%%", averageSuccessRate * 100))
        Total Attempts: \(totalAttempts)
        Successful Attempts: \(totalSuccessful)
        Data Points: \(data.count) days
        """
        statsLabel.numberOfLines = 0
        statsLabel.textColor = .primaryText
        statsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)

        chartView.addSubview(statsLabel)
        statsLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
        }

        return chartView
    }

    private func createLandingDistributionCard() -> UIView {
        let cardView = createCardView()

        let titleLabel = UILabel()
        titleLabel.text = "🧭 Landing Distribution"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = .primaryText

        let analyzer = StatsAnalyzer.shared
        let distribution = analyzer.getLandingDistribution(period: selectedPeriod)

        let distributionView = createLandingDistributionView(distribution: distribution)

        cardView.addSubview(titleLabel)
        cardView.addSubview(distributionView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        distributionView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(120)
        }

        return cardView
    }

    private func createLandingDistributionView(distribution: LandingDistribution) -> UIView {
        let containerView = UIView()

        if distribution.total == 0 {
            let noDataLabel = UILabel()
            noDataLabel.text = "No landing data available"
            noDataLabel.textColor = .secondaryText
            noDataLabel.textAlignment = .center
            noDataLabel.font = UIFont.systemFont(ofSize: 14)

            containerView.addSubview(noDataLabel)
            noDataLabel.snp.makeConstraints { make in
                make.center.equalToSuperview()
            }
            return containerView
        }

        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 8

        // Center
        let centerView = createDistributionItem(
            title: "🎯 Center",
            count: distribution.centerCount,
            percentage: distribution.centerPercentage,
            color: .accentGreen
        )

        // Left
        let leftView = createDistributionItem(
            title: "⬅️ Left",
            count: distribution.leftCount,
            percentage: distribution.leftPercentage,
            color: .accentOrange
        )

        // Right
        let rightView = createDistributionItem(
            title: "➡️ Right",
            count: distribution.rightCount,
            percentage: distribution.rightPercentage,
            color: .accentBlue
        )

        // Out of Bounds
        let outView = createDistributionItem(
            title: "❌ Out",
            count: distribution.outOfBoundsCount,
            percentage: distribution.outOfBoundsPercentage,
            color: .accentRed
        )

        stackView.addArrangedSubview(centerView)
        stackView.addArrangedSubview(leftView)
        stackView.addArrangedSubview(rightView)
        stackView.addArrangedSubview(outView)

        containerView.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        return containerView
    }

    private func createDistributionItem(title: String, count: Int, percentage: Double, color: UIColor) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = color.withAlphaComponent(0.1)
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = color.withAlphaComponent(0.3).cgColor

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = .primaryText
        titleLabel.textAlignment = .center

        let countLabel = UILabel()
        countLabel.text = "\(count)"
        countLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        countLabel.textColor = color
        countLabel.textAlignment = .center

        let percentageLabel = UILabel()
        percentageLabel.text = String(format: "%.1f%%", percentage)
        percentageLabel.font = UIFont.systemFont(ofSize: 11, weight: .medium)
        percentageLabel.textColor = .secondaryText
        percentageLabel.textAlignment = .center

        containerView.addSubview(titleLabel)
        containerView.addSubview(countLabel)
        containerView.addSubview(percentageLabel)

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.leading.trailing.equalToSuperview().inset(4)
        }

        countLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        percentageLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-8)
            make.leading.trailing.equalToSuperview().inset(4)
        }

        return containerView
    }

    private func createTopAttemptsCard() -> UIView {
        let cardView = createCardView()

        let titleLabel = UILabel()
        titleLabel.text = "🏅 Top 5 Best Attempts"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = .primaryText

        let analyzer = StatsAnalyzer.shared
        let topAttempts = analyzer.getTopAttempts(limit: 5)

        let attemptsView = createTopAttemptsView(topAttempts: topAttempts)

        cardView.addSubview(titleLabel)
        cardView.addSubview(attemptsView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        attemptsView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }

        return cardView
    }

    private func createTopAttemptsView(topAttempts: [TopAttempt]) -> UIView {
        let containerView = UIView()

        if topAttempts.isEmpty {
            let noDataLabel = UILabel()
            noDataLabel.text = "No attempts recorded yet"
            noDataLabel.textColor = .secondaryText
            noDataLabel.textAlignment = .center
            noDataLabel.font = UIFont.systemFont(ofSize: 14)

            containerView.addSubview(noDataLabel)
            noDataLabel.snp.makeConstraints { make in
                make.center.equalToSuperview()
                make.height.equalTo(60)
            }
            return containerView
        }

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 8

        for topAttempt in topAttempts {
            let attemptView = createTopAttemptRow(topAttempt: topAttempt)
            stackView.addArrangedSubview(attemptView)
        }

        containerView.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        return containerView
    }

    private func createTopAttemptRow(topAttempt: TopAttempt) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .systemGray6
        containerView.layer.cornerRadius = 8

        let rankLabel = UILabel()
        rankLabel.text = "#\(topAttempt.rank)"
        rankLabel.font = UIFont.systemFont(ofSize: 14, weight: .bold)
        rankLabel.textColor = .systemPurple
        rankLabel.textAlignment = .center

        let ratingLabel = UILabel()
        ratingLabel.text = topAttempt.attempt.starRating
        ratingLabel.font = UIFont.systemFont(ofSize: 16)

        let dateLabel = UILabel()
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        dateLabel.text = formatter.string(from: topAttempt.attempt.date)
        dateLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        dateLabel.textColor = .secondaryText

        let detailsLabel = UILabel()
        detailsLabel.text = "\(topAttempt.attempt.startingPosition.emoji) \(topAttempt.attempt.landingArea.emoji) \(topAttempt.attempt.isSuccessful ? "✅" : "❌")"
        detailsLabel.font = UIFont.systemFont(ofSize: 14)

        containerView.addSubview(rankLabel)
        containerView.addSubview(ratingLabel)
        containerView.addSubview(dateLabel)
        containerView.addSubview(detailsLabel)

        rankLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.equalTo(30)
        }

        ratingLabel.snp.makeConstraints { make in
            make.leading.equalTo(rankLabel.snp.trailing).offset(12)
            make.centerY.equalToSuperview()
        }

        dateLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-12)
            make.top.equalToSuperview().offset(8)
        }

        detailsLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview().offset(-8)
        }

        containerView.snp.makeConstraints { make in
            make.height.equalTo(50)
        }

        return containerView
    }

    private func createErrorStatsCard() -> UIView {
        let cardView = createCardView()

        let titleLabel = UILabel()
        titleLabel.text = "💥 Common Error Types"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = .primaryText

        let analyzer = StatsAnalyzer.shared
        let errorStats = analyzer.getErrorTypeStats(period: selectedPeriod)

        let errorStatsView = createErrorStatsView(errorStats: errorStats)

        cardView.addSubview(titleLabel)
        cardView.addSubview(errorStatsView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        errorStatsView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }

        return cardView
    }

    private func createErrorStatsView(errorStats: [ErrorTypeStats]) -> UIView {
        let containerView = UIView()

        if errorStats.isEmpty {
            let noDataLabel = UILabel()
            noDataLabel.text = "No error patterns detected"
            noDataLabel.textColor = .secondaryText
            noDataLabel.textAlignment = .center
            noDataLabel.font = UIFont.systemFont(ofSize: 14)

            containerView.addSubview(noDataLabel)
            noDataLabel.snp.makeConstraints { make in
                make.center.equalToSuperview()
                make.height.equalTo(60)
            }
            return containerView
        }

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 8

        // Show top 5 error types
        let topErrors = Array(errorStats.prefix(5))

        for (index, errorStat) in topErrors.enumerated() {
            let errorView = createErrorStatRow(errorStat: errorStat, rank: index + 1)
            stackView.addArrangedSubview(errorView)
        }

        containerView.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        return containerView
    }

    private func createErrorStatRow(errorStat: ErrorTypeStats, rank: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .systemGray6
        containerView.layer.cornerRadius = 8

        let rankLabel = UILabel()
        rankLabel.text = "#\(rank)"
        rankLabel.font = UIFont.systemFont(ofSize: 14, weight: .bold)
        rankLabel.textColor = .accentRed
        rankLabel.textAlignment = .center

        let errorTypeLabel = UILabel()
        errorTypeLabel.text = errorStat.errorType
        errorTypeLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        errorTypeLabel.textColor = .primaryText

        let countLabel = UILabel()
        countLabel.text = "\(errorStat.count)"
        countLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        countLabel.textColor = .accentRed

        let percentageLabel = UILabel()
        percentageLabel.text = String(format: "%.1f%%", errorStat.percentage)
        percentageLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        percentageLabel.textColor = .secondaryText

        containerView.addSubview(rankLabel)
        containerView.addSubview(errorTypeLabel)
        containerView.addSubview(countLabel)
        containerView.addSubview(percentageLabel)

        rankLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.equalTo(30)
        }

        errorTypeLabel.snp.makeConstraints { make in
            make.leading.equalTo(rankLabel.snp.trailing).offset(12)
            make.centerY.equalToSuperview()
            make.trailing.lessThanOrEqualTo(countLabel.snp.leading).offset(-8)
        }

        countLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-12)
            make.top.equalToSuperview().offset(8)
        }

        percentageLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview().offset(-8)
        }

        containerView.snp.makeConstraints { make in
            make.height.equalTo(50)
        }

        return containerView
    }

    private func createRatingTrendCard() -> UIView {
        let cardView = createCardView()

        let titleLabel = UILabel()
        titleLabel.text = "📊 Technical Rating Trend"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = .primaryText

        let analyzer = StatsAnalyzer.shared
        let ratingTrend = analyzer.getTechnicalRatingTrend(period: selectedPeriod)

        let trendView = createRatingTrendView(trendData: ratingTrend)

        cardView.addSubview(titleLabel)
        cardView.addSubview(trendView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        trendView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(120)
        }

        return cardView
    }

    private func createRatingTrendView(trendData: [TrendData]) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .systemGray6
        containerView.layer.cornerRadius = 8

        if trendData.isEmpty {
            let noDataLabel = UILabel()
            noDataLabel.text = "No rating trend data available"
            noDataLabel.textColor = .secondaryText
            noDataLabel.textAlignment = .center
            noDataLabel.font = UIFont.systemFont(ofSize: 14)

            containerView.addSubview(noDataLabel)
            noDataLabel.snp.makeConstraints { make in
                make.center.equalToSuperview()
            }
            return containerView
        }

        // Simple text-based trend summary
        let averageRating = trendData.reduce(0) { $0 + $1.value } / Double(trendData.count)
        let maxRating = trendData.max { $0.value < $1.value }?.value ?? 0
        let minRating = trendData.min { $0.value < $1.value }?.value ?? 0

        let trendDirection = trendData.count > 1 ?
            (trendData.last!.value > trendData.first!.value ? "📈 Improving" :
             trendData.last!.value < trendData.first!.value ? "📉 Declining" : "➡️ Stable") : "➡️ Stable"

        let statsLabel = UILabel()
        statsLabel.text = """
        Average Rating: \(String(format: "%.1f", averageRating)) ⭐
        Highest: \(String(format: "%.1f", maxRating)) ⭐
        Lowest: \(String(format: "%.1f", minRating)) ⭐
        Trend: \(trendDirection)
        """
        statsLabel.numberOfLines = 0
        statsLabel.textColor = .primaryText
        statsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)

        containerView.addSubview(statsLabel)
        statsLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(16)
        }

        return containerView
    }
}
