//
//  AppDelegate.swift
//  AttemptLog
//
//  Created by tyuu on 2025/5/24.
//

import UIKit
import IQKeyboardManagerSwift
import IQKeyboardToolbarManager

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        IQKeyboardManager.shared.isEnabled = true
        IQKeyboardToolbarManager.shared.isEnabled = true

        window = UIWindow(frame: UIScreen.main.bounds)

        // Create the main tab bar controller
        let mainTabBarController = MainTabBarController()

        window?.rootViewController = mainTabBarController
        window?.makeKeyAndVisible()
        return true
    }
}

