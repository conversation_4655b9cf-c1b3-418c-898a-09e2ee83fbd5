//
//  AddDisruptionViewController.swift
//  AttemptLog
//
//  Created by tyuu on 2025/5/24.
//

import UIKit
import SnapKit

class AddDisruptionViewController: BaseViewController {
    
    // MARK: - Properties
    
    private var selectedFieldCondition: FieldCondition = .flat
    private var selectedWindCondition: WindCondition = .calm
    private var selectedEquipmentStatus: [EquipmentStatus] = []
    private var selectedPhysicalState: PhysicalState = .normal
    private var selectedTrainingRhythm: TrainingRhythm = .continuous
    
    // MARK: - UI Components
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var contentView = UIView()
    
    private lazy var formStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 20
        return stackView
    }()
    
    private lazy var notesTextView: UITextView = {
        let textView = UITextView()
        textView.backgroundColor = .cardBackground
        textView.layer.cornerRadius = 12
        textView.layer.borderWidth = 1
        textView.layer.borderColor = UIColor.systemGray4.cgColor
        textView.font = UIFont.systemFont(ofSize: 16)
        textView.textColor = .primaryText
        textView.text = "Add additional notes here..."
        textView.textColor = .placeholderText
        textView.delegate = self
        return textView
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
    }
    
    // MARK: - Setup Methods
    
    private func setupNavigationBar() {
        title = "New Disruption Log"
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveTapped)
        )
    }
    
    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(formStackView)
        
        setupFormSections()
        setupConstraints()
    }
    
    private func setupFormSections() {
        // Date section
        let dateCard = createSectionCard(title: "📅 Date", content: createDateSection())
        formStackView.addArrangedSubview(dateCard)
        
        // Field condition section
        let fieldCard = createSectionCard(title: "🔂 Field Condition", content: createFieldConditionSection())
        formStackView.addArrangedSubview(fieldCard)
        
        // Wind condition section
        let windCard = createSectionCard(title: "🌬️ Wind Condition", content: createWindConditionSection())
        formStackView.addArrangedSubview(windCard)
        
        // Equipment status section
        let equipmentCard = createSectionCard(title: "👟 Equipment Status", content: createEquipmentStatusSection())
        formStackView.addArrangedSubview(equipmentCard)
        
        // Physical state section
        let physicalCard = createSectionCard(title: "😵‍💫 Physical State", content: createPhysicalStateSection())
        formStackView.addArrangedSubview(physicalCard)
        
        // Training rhythm section
        let rhythmCard = createSectionCard(title: "🕐 Training Rhythm", content: createTrainingRhythmSection())
        formStackView.addArrangedSubview(rhythmCard)
        
        // Notes section
        let notesCard = createSectionCard(title: "📝 Additional Notes", content: createNotesSection())
        formStackView.addArrangedSubview(notesCard)
    }
    
    private func createSectionCard(title: String, content: UIView) -> UIView {
        let cardView = createCardView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = .primaryText
        
        cardView.addSubview(titleLabel)
        cardView.addSubview(content)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        content.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
        
        return cardView
    }
    
    private func createDateSection() -> UIView {
        let label = UILabel()
        label.text = DateFormatter.localizedString(from: Date(), dateStyle: .full, timeStyle: .short)
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = .secondaryText
        return label
    }
    
    private func createFieldConditionSection() -> UIView {
        return createSegmentedControl(
            items: FieldCondition.allCases.map { "\($0.emoji) \($0.rawValue)" },
            selectedIndex: 0,
            action: #selector(fieldConditionChanged(_:))
        )
    }
    
    private func createWindConditionSection() -> UIView {
        return createSegmentedControl(
            items: WindCondition.allCases.map { "\($0.emoji) \($0.rawValue)" },
            selectedIndex: 3, // Default to calm
            action: #selector(windConditionChanged(_:))
        )
    }
    
    private func createEquipmentStatusSection() -> UIView {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 8
        
        for (index, equipment) in EquipmentStatus.allCases.enumerated() {
            let button = UIButton(type: .system)
            button.setTitle("\(equipment.emoji) \(equipment.rawValue)", for: .normal)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
            button.backgroundColor = .systemGray6
            button.setTitleColor(.primaryText, for: .normal)
            button.layer.cornerRadius = 8
            button.tag = index
            button.addTarget(self, action: #selector(equipmentStatusTapped(_:)), for: .touchUpInside)
            
            button.snp.makeConstraints { make in
                make.height.equalTo(44)
            }
            
            stackView.addArrangedSubview(button)
        }
        
        return stackView
    }
    
    private func createPhysicalStateSection() -> UIView {
        return createSegmentedControl(
            items: PhysicalState.allCases.map { "\($0.emoji) \($0.rawValue)" },
            selectedIndex: 3, // Default to normal
            action: #selector(physicalStateChanged(_:))
        )
    }
    
    private func createTrainingRhythmSection() -> UIView {
        return createSegmentedControl(
            items: TrainingRhythm.allCases.map { "\($0.emoji) \($0.rawValue)" },
            selectedIndex: 0,
            action: #selector(trainingRhythmChanged(_:))
        )
    }
    
    private func createNotesSection() -> UIView {
        notesTextView.snp.makeConstraints { make in
            make.height.equalTo(100)
        }
        return notesTextView
    }
    
    private func createSegmentedControl(items: [String], selectedIndex: Int, action: Selector) -> UIView {
        let segmentedControl = UISegmentedControl(items: items)
        segmentedControl.selectedSegmentIndex = selectedIndex
        segmentedControl.addTarget(self, action: action, for: .valueChanged)
        segmentedControl.backgroundColor = .systemGray6
        segmentedControl.selectedSegmentTintColor = .accentOrange
        segmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        return segmentedControl
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        formStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }
    }
    
    // MARK: - Actions
    
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }
    
    @objc private func saveTapped() {
        let notes = notesTextView.textColor == .placeholderText ? "" : notesTextView.text ?? ""
        
        let disruption = DisruptionLog(
            fieldCondition: selectedFieldCondition,
            windCondition: selectedWindCondition,
            equipmentStatus: selectedEquipmentStatus,
            physicalState: selectedPhysicalState,
            trainingRhythm: selectedTrainingRhythm,
            additionalNotes: notes
        )
        
        DisruptionLogManager.shared.addDisruption(disruption)
        
        dismiss(animated: true)
    }
    
    @objc private func fieldConditionChanged(_ sender: UISegmentedControl) {
        selectedFieldCondition = FieldCondition.allCases[sender.selectedSegmentIndex]
    }
    
    @objc private func windConditionChanged(_ sender: UISegmentedControl) {
        selectedWindCondition = WindCondition.allCases[sender.selectedSegmentIndex]
    }
    
    @objc private func equipmentStatusTapped(_ sender: UIButton) {
        let equipment = EquipmentStatus.allCases[sender.tag]
        
        if selectedEquipmentStatus.contains(equipment) {
            selectedEquipmentStatus.removeAll { $0 == equipment }
            sender.backgroundColor = .systemGray6
            sender.setTitleColor(.primaryText, for: .normal)
        } else {
            selectedEquipmentStatus.append(equipment)
            sender.backgroundColor = .accentOrange
            sender.setTitleColor(.white, for: .normal)
        }
    }
    
    @objc private func physicalStateChanged(_ sender: UISegmentedControl) {
        selectedPhysicalState = PhysicalState.allCases[sender.selectedSegmentIndex]
    }
    
    @objc private func trainingRhythmChanged(_ sender: UISegmentedControl) {
        selectedTrainingRhythm = TrainingRhythm.allCases[sender.selectedSegmentIndex]
    }
}

// MARK: - UITextViewDelegate

extension AddDisruptionViewController: UITextViewDelegate {
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.textColor == .placeholderText {
            textView.text = ""
            textView.textColor = .primaryText
        }
    }
    
    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text.isEmpty {
            textView.text = "Add additional notes here..."
            textView.textColor = .placeholderText
        }
    }
}
