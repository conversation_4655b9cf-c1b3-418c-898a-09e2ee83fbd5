//
//  DisruptionsListViewController.swift
//  AttemptLog
//
//  Created by tyuu on 2025/5/24.
//

import UIKit
import SnapKit

class DisruptionsListViewController: BaseViewController {
    
    // MARK: - Properties
    
    private var disruptions: [DisruptionLog] = []
    
    // MARK: - UI Components
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(DisruptionTableViewCell.self, forCellReuseIdentifier: DisruptionTableViewCell.identifier)
        return tableView
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "wind")
        imageView.tintColor = .lightText.withAlphaComponent(0.6)
        imageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = "No Disruption Records"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = .lightText
        titleLabel.textAlignment = .center
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Start logging environmental and physical factors that affect your training"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = .lightText.withAlphaComponent(0.8)
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        
        view.addSubview(imageView)
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview()
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        return view
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadDisruptions()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadDisruptions()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "All Disruptions"
        
        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(40)
        }
        
        updateEmptyState()
    }
    
    // MARK: - Data Methods
    
    private func loadDisruptions() {
        disruptions = DisruptionLogManager.shared.disruptions
        tableView.reloadData()
        updateEmptyState()
    }
    
    private func updateEmptyState() {
        emptyStateView.isHidden = !disruptions.isEmpty
        tableView.isHidden = disruptions.isEmpty
    }
}

// MARK: - UITableViewDataSource

extension DisruptionsListViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return disruptions.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: DisruptionTableViewCell.identifier, for: indexPath) as! DisruptionTableViewCell
        cell.configure(with: disruptions[indexPath.row])
        return cell
    }
}

// MARK: - UITableViewDelegate

extension DisruptionsListViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let disruption = disruptions[indexPath.row]
        let detailVC = DisruptionDetailViewController(disruption: disruption)
        navigationController?.pushViewController(detailVC, animated: true)
    }
    
    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            DisruptionLogManager.shared.deleteDisruption(at: indexPath.row)
            disruptions.remove(at: indexPath.row)
            tableView.deleteRows(at: [indexPath], with: .fade)
            updateEmptyState()
        }
    }
}

// MARK: - DisruptionTableViewCell

class DisruptionTableViewCell: UITableViewCell {
    static let identifier = "DisruptionTableViewCell"
    
    private lazy var cardView: UIView = {
        let view = UIView()
        view.backgroundColor = .cardBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.cardShadow.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        view.layer.shadowOpacity = 1.0
        return view
    }()
    
    private lazy var dateLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.textColor = .secondaryText
        return label
    }()
    
    private lazy var conditionsLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .primaryText
        label.numberOfLines = 2
        return label
    }()
    
    private lazy var physicalStateLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.textColor = .accentOrange
        return label
    }()
    
    private lazy var notesLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textColor = .secondaryText
        label.numberOfLines = 1
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(cardView)
        cardView.addSubview(dateLabel)
        cardView.addSubview(conditionsLabel)
        cardView.addSubview(physicalStateLabel)
        cardView.addSubview(notesLabel)
        
        cardView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }
        
        dateLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        physicalStateLabel.snp.makeConstraints { make in
            make.centerY.equalTo(dateLabel)
            make.trailing.equalToSuperview().offset(-16)
        }
        
        conditionsLabel.snp.makeConstraints { make in
            make.top.equalTo(dateLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        notesLabel.snp.makeConstraints { make in
            make.top.equalTo(conditionsLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    func configure(with disruption: DisruptionLog) {
        dateLabel.text = disruption.formattedDate
        
        conditionsLabel.text = "\(disruption.fieldCondition.emoji) \(disruption.fieldCondition.rawValue) • \(disruption.windCondition.emoji) \(disruption.windCondition.rawValue) • \(disruption.trainingRhythm.emoji) \(disruption.trainingRhythm.rawValue)"
        
        physicalStateLabel.text = "\(disruption.physicalState.emoji) \(disruption.physicalState.rawValue)"
        
        if disruption.additionalNotes.isEmpty {
            notesLabel.text = "No additional notes"
            notesLabel.textColor = .tertiaryLabel
        } else {
            notesLabel.text = disruption.additionalNotes
            notesLabel.textColor = .secondaryText
        }
    }
}
