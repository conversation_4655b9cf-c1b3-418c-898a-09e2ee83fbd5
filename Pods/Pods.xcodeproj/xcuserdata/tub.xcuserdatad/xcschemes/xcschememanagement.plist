<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>AAInfographics.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>0</integer>
		</dict>
		<key>IQKeyboardCore-IQKeyboardCore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>2</integer>
		</dict>
		<key>IQKeyboardCore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>1</integer>
		</dict>
		<key>IQKeyboardManagerSwift-IQKeyboardManagerSwift.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>4</integer>
		</dict>
		<key>IQKeyboardManagerSwift.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>3</integer>
		</dict>
		<key>IQKeyboardNotification-IQKeyboardNotification.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>6</integer>
		</dict>
		<key>IQKeyboardNotification.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>5</integer>
		</dict>
		<key>IQKeyboardReturnManager-IQKeyboardReturnManager.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>8</integer>
		</dict>
		<key>IQKeyboardReturnManager.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>7</integer>
		</dict>
		<key>IQKeyboardToolbar-IQKeyboardToolbar.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>10</integer>
		</dict>
		<key>IQKeyboardToolbar.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>9</integer>
		</dict>
		<key>IQKeyboardToolbarManager-IQKeyboardToolbarManager.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>12</integer>
		</dict>
		<key>IQKeyboardToolbarManager.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>11</integer>
		</dict>
		<key>IQTextInputViewNotification-IQTextInputViewNotification.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>14</integer>
		</dict>
		<key>IQTextInputViewNotification.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>13</integer>
		</dict>
		<key>IQTextView-IQTextView.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>16</integer>
		</dict>
		<key>IQTextView.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>15</integer>
		</dict>
		<key>Pods-AttemptLog.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>17</integer>
		</dict>
		<key>SnapKit-SnapKit_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>19</integer>
		</dict>
		<key>SnapKit.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
			<key>orderHint</key>
			<integer>18</integer>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
