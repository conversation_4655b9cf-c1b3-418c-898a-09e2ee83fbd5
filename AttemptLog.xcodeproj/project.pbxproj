// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		95B2F2D72DE2058400F78FC3 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B2F2CD2DE2058400F78FC3 /* AppDelegate.swift */; };
		95B2F2DA2DE2058400F78FC3 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 95B2F2CE2DE2058400F78FC3 /* Assets.xcassets */; };
		95B2F2DC2DE2058400F78FC3 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 95B2F2D12DE2058400F78FC3 /* LaunchScreen.storyboard */; };
		95B2F2E52DE2058400F78FC3 /* UIColor+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B2F2DD2DE2058400F78FC3 /* UIColor+Extensions.swift */; };
		95B2F2E62DE2058400F78FC3 /* BaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B2F2DE2DE2058400F78FC3 /* BaseViewController.swift */; };
		95B2F2E72DE2058400F78FC3 /* AttemptLogModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B2F2DF2DE2058400F78FC3 /* AttemptLogModel.swift */; };
		95B2F2E82DE2058400F78FC3 /* MainTabBarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B2F2E02DE2058400F78FC3 /* MainTabBarController.swift */; };
		95B2F2E92DE2058400F78FC3 /* ThrowAttemptLogViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B2F2E12DE2058400F78FC3 /* ThrowAttemptLogViewController.swift */; };
		95B2F2EA2DE2058400F78FC3 /* AddAttemptViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B2F2E22DE2058400F78FC3 /* AddAttemptViewController.swift */; };
		95B2F2EB2DE2058400F78FC3 /* AttemptsListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B2F2E32DE2058400F78FC3 /* AttemptsListViewController.swift */; };
		95B2F2EC2DE2058400F78FC3 /* AttemptDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B2F2E42DE2058400F78FC3 /* AttemptDetailViewController.swift */; };
		CB678E66DEF78615C46CFBC1 /* Pods_AttemptLog.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 391FAAB5B43C8FA4BDD79FF1 /* Pods_AttemptLog.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		06731F76B8569B900DC7C360 /* Pods-AttemptLog.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AttemptLog.release.xcconfig"; path = "Target Support Files/Pods-AttemptLog/Pods-AttemptLog.release.xcconfig"; sourceTree = "<group>"; };
		391FAAB5B43C8FA4BDD79FF1 /* Pods_AttemptLog.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_AttemptLog.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		68E1B3F611C07AA0C92E8D34 /* Pods-AttemptLog.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AttemptLog.debug.xcconfig"; path = "Target Support Files/Pods-AttemptLog/Pods-AttemptLog.debug.xcconfig"; sourceTree = "<group>"; };
		95B2F2B52DE2057F00F78FC3 /* AttemptLog.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = AttemptLog.app; sourceTree = BUILT_PRODUCTS_DIR; };
		95B2F2CD2DE2058400F78FC3 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		95B2F2CE2DE2058400F78FC3 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		95B2F2CF2DE2058400F78FC3 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		95B2F2D02DE2058400F78FC3 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		95B2F2DD2DE2058400F78FC3 /* UIColor+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIColor+Extensions.swift"; sourceTree = "<group>"; };
		95B2F2DE2DE2058400F78FC3 /* BaseViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseViewController.swift; sourceTree = "<group>"; };
		95B2F2DF2DE2058400F78FC3 /* AttemptLogModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AttemptLogModel.swift; sourceTree = "<group>"; };
		95B2F2E02DE2058400F78FC3 /* MainTabBarController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainTabBarController.swift; sourceTree = "<group>"; };
		95B2F2E12DE2058400F78FC3 /* ThrowAttemptLogViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThrowAttemptLogViewController.swift; sourceTree = "<group>"; };
		95B2F2E22DE2058400F78FC3 /* AddAttemptViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddAttemptViewController.swift; sourceTree = "<group>"; };
		95B2F2E32DE2058400F78FC3 /* AttemptsListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AttemptsListViewController.swift; sourceTree = "<group>"; };
		95B2F2E42DE2058400F78FC3 /* AttemptDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AttemptDetailViewController.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		95B2F2B22DE2057F00F78FC3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CB678E66DEF78615C46CFBC1 /* Pods_AttemptLog.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		25E0316E45201F12FA10A000 /* Pods */ = {
			isa = PBXGroup;
			children = (
				68E1B3F611C07AA0C92E8D34 /* Pods-AttemptLog.debug.xcconfig */,
				06731F76B8569B900DC7C360 /* Pods-AttemptLog.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		95B2F2AC2DE2057F00F78FC3 = {
			isa = PBXGroup;
			children = (
				95B2F2D62DE2058400F78FC3 /* AttemptLog */,
				95B2F2B62DE2057F00F78FC3 /* Products */,
				25E0316E45201F12FA10A000 /* Pods */,
				B2779926D63967884FEDDE79 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		95B2F2B62DE2057F00F78FC3 /* Products */ = {
			isa = PBXGroup;
			children = (
				95B2F2B52DE2057F00F78FC3 /* AttemptLog.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		95B2F2D62DE2058400F78FC3 /* AttemptLog */ = {
			isa = PBXGroup;
			children = (
				95B2F2CD2DE2058400F78FC3 /* AppDelegate.swift */,
				95B2F2DD2DE2058400F78FC3 /* UIColor+Extensions.swift */,
				95B2F2DE2DE2058400F78FC3 /* BaseViewController.swift */,
				95B2F2DF2DE2058400F78FC3 /* AttemptLogModel.swift */,
				95B2F2E02DE2058400F78FC3 /* MainTabBarController.swift */,
				95B2F2E12DE2058400F78FC3 /* ThrowAttemptLogViewController.swift */,
				95B2F2E22DE2058400F78FC3 /* AddAttemptViewController.swift */,
				95B2F2E32DE2058400F78FC3 /* AttemptsListViewController.swift */,
				95B2F2E42DE2058400F78FC3 /* AttemptDetailViewController.swift */,
				95B2F2CE2DE2058400F78FC3 /* Assets.xcassets */,
				95B2F2CF2DE2058400F78FC3 /* Info.plist */,
				95B2F2D12DE2058400F78FC3 /* LaunchScreen.storyboard */,
			);
			path = AttemptLog;
			sourceTree = "<group>";
		};
		B2779926D63967884FEDDE79 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				391FAAB5B43C8FA4BDD79FF1 /* Pods_AttemptLog.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		95B2F2B42DE2057F00F78FC3 /* AttemptLog */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 95B2F2C82DE2058100F78FC3 /* Build configuration list for PBXNativeTarget "AttemptLog" */;
			buildPhases = (
				2F386CA57003CB01D6875EED /* [CP] Check Pods Manifest.lock */,
				95B2F2B12DE2057F00F78FC3 /* Sources */,
				95B2F2B22DE2057F00F78FC3 /* Frameworks */,
				95B2F2B32DE2057F00F78FC3 /* Resources */,
				7F0A6E47C9B13F7C70D45D65 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AttemptLog;
			productName = AttemptLog;
			productReference = 95B2F2B52DE2057F00F78FC3 /* AttemptLog.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		95B2F2AD2DE2057F00F78FC3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					95B2F2B42DE2057F00F78FC3 = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 95B2F2B02DE2057F00F78FC3 /* Build configuration list for PBXProject "AttemptLog" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 95B2F2AC2DE2057F00F78FC3;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 95B2F2B62DE2057F00F78FC3 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				95B2F2B42DE2057F00F78FC3 /* AttemptLog */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		95B2F2B32DE2057F00F78FC3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95B2F2DA2DE2058400F78FC3 /* Assets.xcassets in Resources */,
				95B2F2DC2DE2058400F78FC3 /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		2F386CA57003CB01D6875EED /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-AttemptLog-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		7F0A6E47C9B13F7C70D45D65 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AttemptLog/Pods-AttemptLog-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AttemptLog/Pods-AttemptLog-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AttemptLog/Pods-AttemptLog-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		95B2F2B12DE2057F00F78FC3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				95B2F2D72DE2058400F78FC3 /* AppDelegate.swift in Sources */,
				95B2F2E52DE2058400F78FC3 /* UIColor+Extensions.swift in Sources */,
				95B2F2E62DE2058400F78FC3 /* BaseViewController.swift in Sources */,
				95B2F2E72DE2058400F78FC3 /* AttemptLogModel.swift in Sources */,
				95B2F2E82DE2058400F78FC3 /* MainTabBarController.swift in Sources */,
				95B2F2E92DE2058400F78FC3 /* ThrowAttemptLogViewController.swift in Sources */,
				95B2F2EA2DE2058400F78FC3 /* AddAttemptViewController.swift in Sources */,
				95B2F2EB2DE2058400F78FC3 /* AttemptsListViewController.swift in Sources */,
				95B2F2EC2DE2058400F78FC3 /* AttemptDetailViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		95B2F2D12DE2058400F78FC3 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				95B2F2D02DE2058400F78FC3 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		95B2F2C92DE2058100F78FC3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 68E1B3F611C07AA0C92E8D34 /* Pods-AttemptLog.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AttemptLog/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yiiguiugiapp.AttemptLog;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		95B2F2CA2DE2058100F78FC3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 06731F76B8569B900DC7C360 /* Pods-AttemptLog.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AttemptLog/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yiiguiugiapp.AttemptLog;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		95B2F2CB2DE2058100F78FC3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		95B2F2CC2DE2058100F78FC3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		95B2F2B02DE2057F00F78FC3 /* Build configuration list for PBXProject "AttemptLog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95B2F2CB2DE2058100F78FC3 /* Debug */,
				95B2F2CC2DE2058100F78FC3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		95B2F2C82DE2058100F78FC3 /* Build configuration list for PBXNativeTarget "AttemptLog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				95B2F2C92DE2058100F78FC3 /* Debug */,
				95B2F2CA2DE2058100F78FC3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 95B2F2AD2DE2057F00F78FC3 /* Project object */;
}
